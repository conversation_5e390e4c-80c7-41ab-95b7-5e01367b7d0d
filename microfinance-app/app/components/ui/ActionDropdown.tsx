'use client';

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

export interface ActionItem {
  label: string;
  onClick: () => void;
  icon?: React.ReactNode;
  className?: string;
  disabled?: boolean;
  href?: string; // For Link actions
}

interface ActionDropdownProps {
  actions: ActionItem[];
  className?: string;
}

export default function ActionDropdown({ actions, className = '' }: ActionDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Calculate dropdown position with enhanced logic for table contexts
  const calculatePosition = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownWidth = 192; // w-48 = 12rem = 192px
      const dropdownHeight = Math.min(actions.length * 40 + 16, 300);

      // Get viewport dimensions
      const viewportHeight = window.innerHeight;
      const viewportWidth = window.innerWidth;

      // Calculate available space around the button
      const spaceAbove = rect.top;
      const spaceBelow = viewportHeight - rect.bottom;
      const spaceLeft = rect.left;
      const spaceRight = viewportWidth - rect.right;

      // Enhanced vertical positioning logic
      let topPosition: number;

      // For table rows, always try to position above first if there's any reasonable space
      if (spaceAbove >= 100 && (spaceBelow < dropdownHeight || spaceAbove > spaceBelow)) {
        // Position above the button with some margin
        topPosition = rect.top - dropdownHeight - 8 + window.scrollY;
      } else if (spaceBelow >= dropdownHeight) {
        // Position below the button
        topPosition = rect.bottom + 8 + window.scrollY;
      } else if (spaceAbove >= dropdownHeight) {
        // Fallback to above if below doesn't work
        topPosition = rect.top - dropdownHeight - 8 + window.scrollY;
      } else {
        // Emergency positioning - try to fit in viewport
        if (spaceAbove > spaceBelow) {
          topPosition = Math.max(10, rect.top - dropdownHeight + window.scrollY);
        } else {
          topPosition = rect.bottom + 8 + window.scrollY;
        }
      }

      // Enhanced horizontal positioning
      let leftPosition: number;

      // For mobile, center the dropdown
      if (viewportWidth < 768) {
        leftPosition = Math.max(10, Math.min(
          (viewportWidth - dropdownWidth) / 2 + window.scrollX,
          viewportWidth - dropdownWidth - 10 + window.scrollX
        ));
      } else {
        // Desktop positioning - align with button
        if (spaceRight >= dropdownWidth) {
          leftPosition = rect.right - dropdownWidth + window.scrollX;
        } else if (spaceLeft >= dropdownWidth) {
          leftPosition = rect.left + window.scrollX;
        } else {
          leftPosition = Math.max(10, viewportWidth - dropdownWidth - 10 + window.scrollX);
        }
      }

      // Final boundary checks with more aggressive constraints
      topPosition = Math.max(10, Math.min(topPosition, viewportHeight - Math.min(dropdownHeight, 200) - 10 + window.scrollY));
      leftPosition = Math.max(10, Math.min(leftPosition, viewportWidth - dropdownWidth - 10 + window.scrollX));

      setDropdownPosition({
        top: topPosition,
        left: leftPosition,
      });

      // Debug logging
      console.log('ActionDropdown positioning:', {
        buttonRect: rect,
        spaceAbove,
        spaceBelow,
        dropdownHeight,
        finalPosition: { top: topPosition, left: leftPosition },
        viewport: { width: viewportWidth, height: viewportHeight }
      });
    }
  };

  // Close dropdown when clicking outside and handle scroll
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    const handleResize = () => {
      if (isOpen) {
        calculatePosition();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      window.addEventListener('scroll', handleScroll, true); // Use capture to catch all scroll events
      window.addEventListener('resize', handleResize);
      // Use setTimeout to ensure positioning happens after DOM update
      setTimeout(calculatePosition, 0);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      window.removeEventListener('scroll', handleScroll, true);
      window.removeEventListener('resize', handleResize);
    };
  }, [isOpen]);

  const handleActionClick = (action: ActionItem, e: React.MouseEvent) => {
    e.stopPropagation();
    setIsOpen(false);
    if (!action.disabled) {
      action.onClick();
    }
  };

  return (
    <>
      <div
        className={`relative inline-block text-left ${className}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 3-dots trigger button */}
        <button
          ref={buttonRef}
          type="button"
          onClick={() => setIsOpen((prev: boolean) => !prev)}
          className="inline-flex items-center justify-center w-8 h-8 text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 rounded-full hover:bg-gray-100"
          aria-label="Actions"
        >
          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
          </svg>
        </button>
      </div>

      {/* Dropdown menu - rendered as portal */}
      {isOpen && typeof window !== 'undefined' && createPortal(
        <div
          ref={dropdownRef}
          className="fixed w-48 bg-white rounded-md shadow-xl border border-gray-200 focus:outline-none"
          style={{
            top: `${dropdownPosition.top}px`,
            left: `${dropdownPosition.left}px`,
            zIndex: 99999,
            boxShadow: '0 20px 40px -4px rgba(0, 0, 0, 0.25), 0 8px 16px -4px rgba(0, 0, 0, 0.1)',
            maxHeight: '300px',
            overflowY: 'auto',
            transform: 'translateZ(0)', // Force hardware acceleration
            willChange: 'transform' // Optimize for animations
          }}
        >
          <div className="py-1">
            {actions.map((action, index) => {
              if (action.href) {
                // Link action
                return (
                  <a
                    key={index}
                    href={action.href}
                    onClick={(e) => handleActionClick(action, e)}
                    className={`group flex items-center px-4 py-2 text-sm transition-colors ${
                      action.disabled
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    } ${action.className || ''}`}
                  >
                    {action.icon && (
                      <span className="mr-3 flex-shrink-0">
                        {action.icon}
                      </span>
                    )}
                    {action.label}
                  </a>
                );
              } else {
                // Button action
                return (
                  <button
                    key={index}
                    onClick={(e) => handleActionClick(action, e)}
                    disabled={action.disabled}
                    className={`group flex items-center w-full px-4 py-2 text-sm text-left transition-colors ${
                      action.disabled
                        ? 'text-gray-400 cursor-not-allowed'
                        : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    } ${action.className || ''}`}
                  >
                    {action.icon && (
                      <span className="mr-3 flex-shrink-0">
                        {action.icon}
                      </span>
                    )}
                    {action.label}
                  </button>
                );
              }
            })}
          </div>
        </div>,
        document.body
      )}
    </>
  );
}
